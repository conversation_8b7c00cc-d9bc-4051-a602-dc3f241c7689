public class Theme {
  public static boolean isDark = false;
  public static int primaryColor = 0x181818;
  public static int backgroundColor = 0xF0F0F0;

  public static boolean isDark() {
    return isDark;
  }

  public static void setDark(boolean isDark) {
    Theme.isDark = isDark;
    if (isDark) {
      setBackgroundColor(0x1A1A1A);
      setPrimaryColor(0x410A4A);
    } else {
      setBackgroundColor(0xF0F0F0);
      setPrimaryColor(0x181818);
    }
  }

  public static int getBackgroundColor() {
    return backgroundColor;
  }

  public static void setBackgroundColor(int backgroundColor) {
    Theme.backgroundColor = backgroundColor;
  }

  public static int getPrimaryColor() {
    return primaryColor;
  }

  public static void setPrimaryColor(int primaryColor) {
    Theme.primaryColor = primaryColor;
  }
}
